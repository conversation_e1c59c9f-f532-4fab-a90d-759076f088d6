[project]
name = "a2a-sample-agent-langgraph"
version = "0.1.0"
description = "Sample LangGraph currency agent with A2A Protocol"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "a2a-sdk>=0.2.5,<0.3.0",
    "click>=8.1.8",
    "httpx>=0.28.1",
    "langgraph>=0.3.18",
    "pydantic>=2.10.6",
    "python-dotenv>=1.1.0",
    "uvicorn>=0.34.2",
    "fastapi>=0.115.12",
    "langchain-openai>=0.3.21",
    "toml>=0.10.2",
    "langgraph-checkpoint-sqlite>=2.0.10",
    "langgraph-checkpoint-postgres>=2.0.21",
    "psycopg>=3.2.9",
    "psycopg-binary>=3.1.9",
    "langchain-postgres>=0.0.14",
]

[tool.hatch.build.targets.wheel]
packages = ["app"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
