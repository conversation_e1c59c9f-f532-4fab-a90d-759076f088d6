[2025-06-13 10:15:43,389] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-13 10:21:18,636] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-13 10:21:18,636] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-13 10:25:12,916] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-13 10:25:12,916] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-13 10:27:27,967] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-13 10:27:27,967] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-13 10:29:58,801] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-13 10:29:58,801] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-13 10:29:58,809] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-13 10:29:58,809] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-13 10:30:00,189] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: The SqliteSaver does not support async methods. Consider using AsyncSqliteSaver instead.
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
Note: AsyncSqliteSaver requires the aiosqlite package to use.
Install with:
`pip install aiosqlite`
See https://langchain-ai.github.io/langgraph/reference/checkpoints/asyncsqlitesaverfor more information.
[2025-06-13 10:30:00,189] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: The SqliteSaver does not support async methods. Consider using AsyncSqliteSaver instead.
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
Note: AsyncSqliteSaver requires the aiosqlite package to use.
Install with:
`pip install aiosqlite`
See https://langchain-ai.github.io/langgraph/reference/checkpoints/asyncsqlitesaverfor more information.
[2025-06-13 10:31:29,726] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-13 10:31:29,726] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-13 10:31:29,726] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-13 10:31:29,726] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-13 10:34:00,252] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-13 10:34:00,252] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-13 10:34:00,252] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-13 10:34:00,252] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-13 10:35:37,041] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-13 10:35:37,041] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-13 10:35:37,041] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-13 10:35:37,041] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-13 10:38:17,978] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-13 10:38:17,979] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-13 10:38:34,726] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-13 10:38:34,726] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-13 10:38:41,506] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: The SqliteSaver does not support async methods. Consider using AsyncSqliteSaver instead.
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
Note: AsyncSqliteSaver requires the aiosqlite package to use.
Install with:
`pip install aiosqlite`
See https://langchain-ai.github.io/langgraph/reference/checkpoints/asyncsqlitesaverfor more information.
[2025-06-13 10:38:41,506] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: The SqliteSaver does not support async methods. Consider using AsyncSqliteSaver instead.
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
Note: AsyncSqliteSaver requires the aiosqlite package to use.
Install with:
`pip install aiosqlite`
See https://langchain-ai.github.io/langgraph/reference/checkpoints/asyncsqlitesaverfor more information.
[2025-06-13 10:40:52,889] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-13 10:40:52,889] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-13 10:41:49,940] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-13 10:41:49,940] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-13 10:41:49,940] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-13 10:41:49,940] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-13 10:48:35,136] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'coroutine' object has no attribute 'get_next_version'
[2025-06-13 10:48:35,136] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'coroutine' object has no attribute 'get_next_version'
[2025-06-13 10:51:02,869] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'coroutine' object has no attribute 'get_next_version'
[2025-06-13 10:51:02,869] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'coroutine' object has no attribute 'get_next_version'
[2025-06-13 13:35:42,923] [MainThread] INFO [memory_util.py:42] [NA] - memory_type is sqlite
[2025-06-13 13:35:42,923] [MainThread] INFO [memory_util.py:42] [NA] - memory_type is sqlite
[2025-06-13 13:35:42,923] [MainThread] INFO [memory_util.py:54] [NA] - sqlite path is data/memory.db
[2025-06-13 13:35:42,923] [MainThread] INFO [memory_util.py:54] [NA] - sqlite path is data/memory.db
[2025-06-13 13:38:00,910] [MainThread] INFO [memory_util.py:42] [NA] - memory_type is sqlite
[2025-06-13 13:38:00,910] [MainThread] INFO [memory_util.py:42] [NA] - memory_type is sqlite
[2025-06-13 13:38:00,910] [MainThread] INFO [memory_util.py:54] [NA] - sqlite path is data/memory.db
[2025-06-13 13:38:00,910] [MainThread] INFO [memory_util.py:54] [NA] - sqlite path is data/memory.db
[2025-06-13 13:39:55,420] [MainThread] INFO [memory_util.py:37] [NA] - memory_type is postgres
[2025-06-13 13:39:55,420] [MainThread] INFO [memory_util.py:37] [NA] - memory_type is postgres
[2025-06-13 13:41:01,057] [MainThread] INFO [memory_util.py:37] [NA] - memory_type is postgres
[2025-06-13 13:41:01,057] [MainThread] INFO [memory_util.py:37] [NA] - memory_type is postgres
[2025-06-13 13:41:34,496] [MainThread] INFO [memory_util.py:37] [NA] - memory_type is postgres
[2025-06-13 13:41:34,496] [MainThread] INFO [memory_util.py:37] [NA] - memory_type is postgres
[2025-06-13 13:43:15,932] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 13:43:15,932] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 14:18:19,854] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:18:19,854] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:18:25,250] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_GeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 14:18:25,250] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_GeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 14:23:32,391] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 14:23:32,391] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 14:31:57,809] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:31:57,809] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:32:05,168] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_GeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 14:32:05,168] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_GeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 14:32:30,034] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:32:30,034] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:32:31,099] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_GeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 14:32:31,099] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_GeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 14:32:55,844] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:32:55,844] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:32:55,844] [MainThread] INFO [memory_util.py:60] [NA] - postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 14:32:55,844] [MainThread] INFO [memory_util.py:60] [NA] - postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 14:34:57,741] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:34:57,741] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:34:57,741] [MainThread] INFO [memory_util.py:60] [NA] - postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 14:34:57,741] [MainThread] INFO [memory_util.py:60] [NA] - postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 14:35:48,488] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_GeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 14:35:48,488] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_GeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 15:03:55,027] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 15:03:55,027] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 15:03:55,027] [MainThread] INFO [memory_util.py:60] [NA] - postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 15:03:55,027] [MainThread] INFO [memory_util.py:60] [NA] - postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 15:14:51,970] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 15:14:51,971] [MainThread] INFO [memory_util.py:60] [NA] - postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 15:19:08,399] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 15:19:08,399] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 15:19:08,400] [MainThread] INFO [memory_util.py:60] [NA] - postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 15:19:08,400] [MainThread] INFO [memory_util.py:60] [NA] - postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 15:21:21,615] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 
[2025-06-13 15:21:21,615] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 
[2025-06-13 15:27:45,640] [MainThread] INFO [memory_util.py:40] [NA] - memory_type is postgres
[2025-06-13 15:27:45,640] [MainThread] INFO [memory_util.py:40] [NA] - memory_type is postgres
[2025-06-13 15:28:45,608] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 15:28:45,608] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 15:29:13,899] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 15:29:13,899] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 15:29:19,109] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_AsyncGeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 15:29:19,109] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_AsyncGeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 15:29:37,807] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 15:29:37,807] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 15:33:06,949] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 15:33:06,949] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 15:33:27,772] [MainThread] INFO [memory_util.py:40] [NA] - memory_type is postgres
[2025-06-13 15:33:27,772] [MainThread] INFO [memory_util.py:40] [NA] - memory_type is postgres
[2025-06-13 15:35:40,844] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 
[2025-06-13 15:35:40,844] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 
[2025-06-13 15:43:26,401] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'coroutine' object has no attribute 'stream'
[2025-06-13 15:43:26,401] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'coroutine' object has no attribute 'stream'
[2025-06-13 15:44:08,090] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'coroutine' object has no attribute 'stream'
[2025-06-13 15:44:08,090] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'coroutine' object has no attribute 'stream'
[2025-06-13 15:51:04,094] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 15:51:04,094] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 15:52:45,580] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 15:52:45,580] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 16:00:53,314] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 16:00:53,314] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 16:06:43,400] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 16:06:43,400] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 16:07:16,498] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 16:07:16,498] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 16:09:31,348] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: the connection is closed
[2025-06-13 16:09:31,348] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: the connection is closed
[2025-06-13 16:26:13,194] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 16:26:13,194] [MainThread] INFO [memory_util.py:88] [NA] - memory_type is postgres
[2025-06-13 16:26:25,908] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_AsyncGeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 16:26:25,908] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_AsyncGeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 16:28:29,022] [MainThread] INFO [memory_util.py:89] [NA] - memory_type is postgres
[2025-06-13 16:28:29,022] [MainThread] INFO [memory_util.py:89] [NA] - memory_type is postgres
[2025-06-13 16:28:40,642] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_AsyncGeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 16:28:40,642] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_AsyncGeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 16:48:13,898] [MainThread] INFO [memory_util.py:89] [NA] - memory_type is postgres
[2025-06-13 16:48:13,898] [MainThread] INFO [memory_util.py:89] [NA] - memory_type is postgres
[2025-06-13 16:48:51,539] [MainThread] INFO [memory_util.py:89] [NA] - memory_type is postgres
[2025-06-13 16:48:51,539] [MainThread] INFO [memory_util.py:89] [NA] - memory_type is postgres
[2025-06-13 16:58:16,326] [MainThread] INFO [memory_util.py:41] [NA] - memory_type is postgres
[2025-06-13 16:58:16,326] [MainThread] INFO [memory_util.py:41] [NA] - memory_type is postgres
[2025-06-13 17:00:30,427] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 
[2025-06-13 17:00:30,427] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 
[2025-06-13 17:14:59,769] [MainThread] INFO [memory_util.py:41] [NA] - memory_type is postgres
[2025-06-13 17:14:59,769] [MainThread] INFO [memory_util.py:41] [NA] - memory_type is postgres
[2025-06-13 17:17:16,155] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 
[2025-06-13 17:17:16,155] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 
[2025-06-13 17:51:23,520] [MainThread] INFO [memory_util.py:89] [NA] - memory_type is postgres
[2025-06-13 17:51:23,520] [MainThread] INFO [memory_util.py:89] [NA] - memory_type is postgres
[2025-06-13 17:51:23,521] [MainThread] INFO [memory_util.py:109] [NA] - postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 17:51:23,521] [MainThread] INFO [memory_util.py:109] [NA] - postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 17:51:34,986] [MainThread] INFO [memory_util.py:89] [NA] - memory_type is postgres
[2025-06-13 17:51:34,986] [MainThread] INFO [memory_util.py:89] [NA] - memory_type is postgres
[2025-06-13 17:51:34,986] [MainThread] INFO [memory_util.py:109] [NA] - postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 17:51:34,986] [MainThread] INFO [memory_util.py:109] [NA] - postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 17:51:38,366] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_AsyncGeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-13 17:51:38,366] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_AsyncGeneratorContextManager' object has no attribute 'get_next_version'
