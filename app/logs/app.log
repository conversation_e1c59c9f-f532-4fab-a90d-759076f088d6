[2025-06-16 09:31:47,900] [MainThread] INFO [memory_util.py:86] [NA] - async memory_type is memory
[2025-06-16 09:31:47,900] [MainThread] INFO [memory_util.py:86] [NA] - async memory_type is memory
[2025-06-16 09:32:36,181] [MainThread] INFO [memory_util.py:86] [NA] - async memory_type is memory
[2025-06-16 09:32:36,181] [MainThread] INFO [memory_util.py:86] [NA] - async memory_type is memory
[2025-06-16 09:33:01,908] [MainThread] INFO [memory_util.py:86] [NA] - async memory_type is memory
[2025-06-16 09:33:01,908] [MainThread] INFO [memory_util.py:86] [NA] - async memory_type is memory
[2025-06-16 09:34:29,098] [MainThread] INFO [memory_util.py:86] [NA] - async memory_type is memory
[2025-06-16 09:34:29,098] [MainThread] INFO [memory_util.py:86] [NA] - async memory_type is memory
[2025-06-16 09:36:18,946] [MainThread] INFO [memory_util.py:86] [NA] - async memory_type is postgres
[2025-06-16 09:36:18,946] [MainThread] INFO [memory_util.py:86] [NA] - async memory_type is postgres
[2025-06-16 09:36:18,947] [MainThread] INFO [memory_util.py:106] [NA] - async postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-16 09:36:18,947] [MainThread] INFO [memory_util.py:106] [NA] - async postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-16 09:36:18,947] [MainThread] WARNING [memory_util.py:109] [NA] - 异步PostgreSQL暂不支持，使用内存存储作为后备
[2025-06-16 09:36:18,947] [MainThread] WARNING [memory_util.py:109] [NA] - 异步PostgreSQL暂不支持，使用内存存储作为后备
[2025-06-16 09:36:18,968] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_AsyncGeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-16 09:36:18,968] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: '_AsyncGeneratorContextManager' object has no attribute 'get_next_version'
[2025-06-16 09:39:36,590] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: Psycopg cannot use the 'ProactorEventLoop' to run in async mode. Please use a compatible event loop, for instance by setting 'asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())'
[2025-06-16 09:39:36,590] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: Psycopg cannot use the 'ProactorEventLoop' to run in async mode. Please use a compatible event loop, for instance by setting 'asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())'
[2025-06-16 09:40:43,430] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: Psycopg cannot use the 'ProactorEventLoop' to run in async mode. Please use a compatible event loop, for instance by setting 'asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())'
[2025-06-16 09:40:43,430] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: Psycopg cannot use the 'ProactorEventLoop' to run in async mode. Please use a compatible event loop, for instance by setting 'asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())'
[2025-06-16 09:43:47,159] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: the connection is closed
[2025-06-16 09:43:47,159] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: the connection is closed
[2025-06-16 09:59:55,831] [MainThread] INFO [memory_util.py:88] [NA] - async memory_type is sqlite
[2025-06-16 09:59:55,831] [MainThread] INFO [memory_util.py:88] [NA] - async memory_type is sqlite
[2025-06-16 09:59:55,832] [MainThread] INFO [memory_util.py:100] [NA] - async sqlite path is data/memory.db
[2025-06-16 09:59:55,832] [MainThread] INFO [memory_util.py:100] [NA] - async sqlite path is data/memory.db
[2025-06-16 09:59:55,870] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'str' object has no attribute 'is_alive'
[2025-06-16 09:59:55,870] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'str' object has no attribute 'is_alive'
[2025-06-16 10:00:36,638] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'coroutine' object does not support the asynchronous context manager protocol
[2025-06-16 10:00:36,638] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'coroutine' object does not support the asynchronous context manager protocol
[2025-06-16 10:49:53,833] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'coroutine' object does not support the asynchronous context manager protocol
[2025-06-16 10:49:53,833] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'coroutine' object does not support the asynchronous context manager protocol
[2025-06-16 10:50:22,189] [MainThread] INFO [memory_util.py:88] [NA] - async memory_type is sqlite
[2025-06-16 10:50:22,189] [MainThread] INFO [memory_util.py:88] [NA] - async memory_type is sqlite
[2025-06-16 10:50:22,190] [MainThread] INFO [memory_util.py:100] [NA] - async sqlite path is data/memory.db
[2025-06-16 10:50:22,190] [MainThread] INFO [memory_util.py:100] [NA] - async sqlite path is data/memory.db
[2025-06-16 10:50:22,206] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'AsyncSqliteSaver' object does not support the asynchronous context manager protocol
[2025-06-16 10:50:22,206] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'AsyncSqliteSaver' object does not support the asynchronous context manager protocol
[2025-06-16 10:50:47,573] [MainThread] INFO [memory_util.py:88] [NA] - async memory_type is postgres
[2025-06-16 10:50:47,573] [MainThread] INFO [memory_util.py:88] [NA] - async memory_type is postgres
[2025-06-16 10:50:47,574] [MainThread] INFO [memory_util.py:108] [NA] - async postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-16 10:50:47,574] [MainThread] INFO [memory_util.py:108] [NA] - async postgres conn_string is postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-16 10:50:47,574] [MainThread] WARNING [memory_util.py:111] [NA] - 异步PostgreSQL暂不支持，使用内存存储作为后备
[2025-06-16 10:50:47,574] [MainThread] WARNING [memory_util.py:111] [NA] - 异步PostgreSQL暂不支持，使用内存存储作为后备
[2025-06-16 10:51:31,943] [MainThread] INFO [memory_util.py:43] [NA] - memory_type is postgres
[2025-06-16 10:51:31,943] [MainThread] INFO [memory_util.py:43] [NA] - memory_type is postgres
[2025-06-16 10:51:31,944] [MainThread] WARNING [memory_util.py:62] [NA] - PostgreSQL同步模式暂不支持，使用内存存储作为后备
[2025-06-16 10:51:31,944] [MainThread] WARNING [memory_util.py:62] [NA] - PostgreSQL同步模式暂不支持，使用内存存储作为后备
[2025-06-16 10:51:31,958] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'ExitStack' object has no attribute 'get_next_version'
[2025-06-16 10:51:31,958] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'ExitStack' object has no attribute 'get_next_version'
[2025-06-16 10:55:59,510] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: couldn't get a connection after 30.00 sec
[2025-06-16 10:55:59,510] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: couldn't get a connection after 30.00 sec
[2025-06-16 10:56:55,739] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: couldn't get a connection after 30.00 sec
[2025-06-16 10:56:55,739] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: couldn't get a connection after 30.00 sec
[2025-06-16 10:59:18,383] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: couldn't get a connection after 30.00 sec
[2025-06-16 10:59:18,383] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: couldn't get a connection after 30.00 sec
[2025-06-16 11:08:20,830] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'StateGraph' object has no attribute 'astream_events'
[2025-06-16 11:08:20,830] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'StateGraph' object has no attribute 'astream_events'
[2025-06-16 11:10:09,766] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: couldn't get a connection after 30.00 sec
[2025-06-16 11:10:09,766] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: couldn't get a connection after 30.00 sec
[2025-06-16 11:12:25,270] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: couldn't get a connection after 30.00 sec
[2025-06-16 11:12:25,270] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: couldn't get a connection after 30.00 sec
[2025-06-16 13:06:43,422] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: couldn't get a connection after 30.00 sec
[2025-06-16 13:06:43,422] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: couldn't get a connection after 30.00 sec
[2025-06-16 13:07:26,842] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'StateGraph' object has no attribute 'aget_state'
[2025-06-16 13:07:26,842] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: 'StateGraph' object has no attribute 'aget_state'
[2025-06-16 13:33:58,512] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: name 'AsyncConnectionPool' is not defined
[2025-06-16 13:33:58,512] [MainThread] ERROR [agent_executor.py:73] [NA] - An error occurred while streaming the response: name 'AsyncConnectionPool' is not defined
[2025-06-16 13:34:37,771] [MainThread] INFO [memory_util.py:138] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:34:37,771] [MainThread] INFO [memory_util.py:138] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:35:07,496] [MainThread] INFO [memory_util.py:138] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:35:07,496] [MainThread] INFO [memory_util.py:138] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:35:07,547] [MainThread] INFO [memory_util.py:151] [NA] - PostgreSQL连接池已创建
[2025-06-16 13:35:07,547] [MainThread] INFO [memory_util.py:151] [NA] - PostgreSQL连接池已创建
[2025-06-16 13:35:07,550] [MainThread] INFO [memory_util.py:211] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:35:07,550] [MainThread] INFO [memory_util.py:211] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:35:25,577] [MainThread] INFO [memory_util.py:215] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:35:25,577] [MainThread] INFO [memory_util.py:215] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:35:25,577] [MainThread] INFO [memory_util.py:155] [NA] - PostgreSQL连接池已关闭
[2025-06-16 13:35:25,577] [MainThread] INFO [memory_util.py:155] [NA] - PostgreSQL连接池已关闭
[2025-06-16 13:35:25,578] [MainThread] INFO [memory_util.py:138] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:35:25,578] [MainThread] INFO [memory_util.py:138] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:35:25,603] [MainThread] INFO [memory_util.py:151] [NA] - PostgreSQL连接池已创建
[2025-06-16 13:35:25,603] [MainThread] INFO [memory_util.py:151] [NA] - PostgreSQL连接池已创建
[2025-06-16 13:35:25,605] [MainThread] INFO [memory_util.py:211] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:35:25,605] [MainThread] INFO [memory_util.py:211] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:35:25,609] [MainThread] INFO [memory_util.py:215] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:35:25,609] [MainThread] INFO [memory_util.py:215] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:35:25,610] [MainThread] INFO [memory_util.py:155] [NA] - PostgreSQL连接池已关闭
[2025-06-16 13:35:25,610] [MainThread] INFO [memory_util.py:155] [NA] - PostgreSQL连接池已关闭
[2025-06-16 13:35:43,598] [MainThread] INFO [memory_util.py:138] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:35:43,598] [MainThread] INFO [memory_util.py:138] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:35:43,632] [MainThread] INFO [memory_util.py:151] [NA] - PostgreSQL连接池已创建
[2025-06-16 13:35:43,632] [MainThread] INFO [memory_util.py:151] [NA] - PostgreSQL连接池已创建
[2025-06-16 13:35:43,635] [MainThread] INFO [memory_util.py:211] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:35:43,635] [MainThread] INFO [memory_util.py:211] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:35:57,556] [MainThread] INFO [memory_util.py:215] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:35:57,556] [MainThread] INFO [memory_util.py:215] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:35:57,556] [MainThread] INFO [memory_util.py:155] [NA] - PostgreSQL连接池已关闭
[2025-06-16 13:35:57,556] [MainThread] INFO [memory_util.py:155] [NA] - PostgreSQL连接池已关闭
[2025-06-16 13:35:57,557] [MainThread] INFO [memory_util.py:138] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:35:57,557] [MainThread] INFO [memory_util.py:138] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:35:57,611] [MainThread] INFO [memory_util.py:151] [NA] - PostgreSQL连接池已创建
[2025-06-16 13:35:57,611] [MainThread] INFO [memory_util.py:151] [NA] - PostgreSQL连接池已创建
[2025-06-16 13:35:57,611] [MainThread] INFO [memory_util.py:211] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:35:57,611] [MainThread] INFO [memory_util.py:211] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:35:57,636] [MainThread] INFO [memory_util.py:215] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:35:57,636] [MainThread] INFO [memory_util.py:215] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:35:57,636] [MainThread] INFO [memory_util.py:155] [NA] - PostgreSQL连接池已关闭
[2025-06-16 13:35:57,636] [MainThread] INFO [memory_util.py:155] [NA] - PostgreSQL连接池已关闭
[2025-06-16 13:36:32,447] [MainThread] INFO [memory_util.py:138] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:36:32,447] [MainThread] INFO [memory_util.py:138] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:36:32,510] [MainThread] INFO [memory_util.py:151] [NA] - PostgreSQL连接池已创建
[2025-06-16 13:36:32,510] [MainThread] INFO [memory_util.py:151] [NA] - PostgreSQL连接池已创建
[2025-06-16 13:36:32,513] [MainThread] INFO [memory_util.py:211] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:36:32,513] [MainThread] INFO [memory_util.py:211] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:36:36,234] [MainThread] INFO [memory_util.py:215] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:36:36,234] [MainThread] INFO [memory_util.py:215] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:36:36,235] [MainThread] INFO [memory_util.py:155] [NA] - PostgreSQL连接池已关闭
[2025-06-16 13:36:36,235] [MainThread] INFO [memory_util.py:155] [NA] - PostgreSQL连接池已关闭
[2025-06-16 13:41:51,922] [MainThread] INFO [memory_util.py:137] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:41:51,967] [MainThread] INFO [memory_util.py:150] [NA] - PostgreSQL连接池已创建
[2025-06-16 13:41:51,969] [MainThread] INFO [memory_util.py:210] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:41:59,879] [MainThread] INFO [memory_util.py:214] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:41:59,880] [MainThread] INFO [memory_util.py:154] [NA] - PostgreSQL连接池已关闭
[2025-06-16 13:41:59,882] [MainThread] INFO [memory_util.py:137] [NA] - 创建异步PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:41:59,966] [MainThread] INFO [memory_util.py:150] [NA] - PostgreSQL连接池已创建
[2025-06-16 13:41:59,967] [MainThread] INFO [memory_util.py:210] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:42:00,043] [MainThread] INFO [memory_util.py:214] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:42:00,043] [MainThread] INFO [memory_util.py:154] [NA] - PostgreSQL连接池已关闭
[2025-06-16 13:54:20,918] [MainThread] INFO [memory_util.py:138] [NA] - 创建全局PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:54:20,919] [MainThread] INFO [memory_util.py:153] [NA] - 全局PostgreSQL连接池已创建并打开
[2025-06-16 13:54:20,950] [MainThread] DEBUG [memory_util.py:209] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 13:54:20,954] [MainThread] INFO [memory_util.py:269] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:54:31,100] [MainThread] INFO [memory_util.py:273] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:54:31,100] [MainThread] DEBUG [memory_util.py:213] [NA] - PostgreSQL保存器已释放
[2025-06-16 13:54:31,100] [MainThread] DEBUG [memory_util.py:209] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 13:54:31,100] [MainThread] INFO [memory_util.py:269] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:54:31,102] [MainThread] DEBUG [memory_util.py:209] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 13:54:31,102] [MainThread] INFO [memory_util.py:269] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:54:31,106] [MainThread] INFO [memory_util.py:273] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:54:31,106] [MainThread] DEBUG [memory_util.py:213] [NA] - PostgreSQL保存器已释放
[2025-06-16 13:54:31,106] [MainThread] INFO [memory_util.py:273] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:54:31,106] [MainThread] DEBUG [memory_util.py:213] [NA] - PostgreSQL保存器已释放
[2025-06-16 13:54:45,263] [MainThread] DEBUG [memory_util.py:209] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 13:54:45,264] [MainThread] INFO [memory_util.py:269] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:54:56,937] [MainThread] INFO [memory_util.py:273] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:54:56,938] [MainThread] DEBUG [memory_util.py:213] [NA] - PostgreSQL保存器已释放
[2025-06-16 13:54:56,938] [MainThread] DEBUG [memory_util.py:209] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 13:54:56,938] [MainThread] INFO [memory_util.py:269] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:54:56,938] [MainThread] DEBUG [memory_util.py:209] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 13:54:56,938] [MainThread] INFO [memory_util.py:269] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:54:56,941] [MainThread] INFO [memory_util.py:273] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:54:56,941] [MainThread] DEBUG [memory_util.py:213] [NA] - PostgreSQL保存器已释放
[2025-06-16 13:54:56,941] [MainThread] INFO [memory_util.py:273] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:54:56,941] [MainThread] DEBUG [memory_util.py:213] [NA] - PostgreSQL保存器已释放
[2025-06-16 13:57:12,931] [MainThread] INFO [memory_util.py:138] [NA] - 创建全局PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:57:12,932] [MainThread] INFO [memory_util.py:153] [NA] - 全局PostgreSQL连接池已创建并打开
[2025-06-16 13:57:12,996] [MainThread] DEBUG [memory_util.py:209] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 13:57:12,998] [MainThread] INFO [memory_util.py:269] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:57:23,337] [MainThread] INFO [memory_util.py:273] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:57:23,337] [MainThread] DEBUG [memory_util.py:213] [NA] - PostgreSQL保存器已释放
[2025-06-16 13:57:23,337] [MainThread] DEBUG [memory_util.py:209] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 13:57:23,337] [MainThread] INFO [memory_util.py:269] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:57:23,338] [MainThread] DEBUG [memory_util.py:209] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 13:57:23,338] [MainThread] INFO [memory_util.py:269] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:57:23,342] [MainThread] INFO [memory_util.py:273] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:57:23,342] [MainThread] DEBUG [memory_util.py:213] [NA] - PostgreSQL保存器已释放
[2025-06-16 13:57:23,343] [MainThread] INFO [memory_util.py:273] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:57:23,343] [MainThread] DEBUG [memory_util.py:213] [NA] - PostgreSQL保存器已释放
[2025-06-16 13:59:15,398] [MainThread] INFO [memory_util.py:138] [NA] - 创建全局PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 13:59:15,399] [MainThread] INFO [memory_util.py:153] [NA] - 全局PostgreSQL连接池已创建并打开
[2025-06-16 13:59:15,432] [MainThread] DEBUG [memory_util.py:194] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 13:59:15,435] [MainThread] INFO [memory_util.py:254] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:59:26,107] [MainThread] INFO [memory_util.py:258] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:59:26,107] [MainThread] DEBUG [memory_util.py:198] [NA] - PostgreSQL保存器已释放
[2025-06-16 13:59:26,107] [MainThread] DEBUG [memory_util.py:194] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 13:59:26,107] [MainThread] INFO [memory_util.py:254] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:59:26,107] [MainThread] DEBUG [memory_util.py:194] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 13:59:26,107] [MainThread] INFO [memory_util.py:254] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 13:59:26,112] [MainThread] INFO [memory_util.py:258] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:59:26,112] [MainThread] DEBUG [memory_util.py:198] [NA] - PostgreSQL保存器已释放
[2025-06-16 13:59:26,112] [MainThread] INFO [memory_util.py:258] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 13:59:26,112] [MainThread] DEBUG [memory_util.py:198] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:00:50,265] [MainThread] INFO [memory_util.py:138] [NA] - 创建全局PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 14:00:50,266] [MainThread] INFO [memory_util.py:153] [NA] - 全局PostgreSQL连接池已创建并打开
[2025-06-16 14:00:50,298] [MainThread] DEBUG [memory_util.py:194] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:01:00,288] [MainThread] DEBUG [memory_util.py:198] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:01:00,288] [MainThread] DEBUG [memory_util.py:194] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:01:00,289] [MainThread] INFO [memory_util.py:254] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 14:01:00,289] [MainThread] DEBUG [memory_util.py:194] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:01:00,289] [MainThread] INFO [memory_util.py:254] [NA] - 图已使用PostgreSQL检查点编译
[2025-06-16 14:01:00,294] [MainThread] INFO [memory_util.py:258] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 14:01:00,294] [MainThread] DEBUG [memory_util.py:198] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:01:00,294] [MainThread] INFO [memory_util.py:258] [NA] - PostgreSQL编译图上下文已关闭
[2025-06-16 14:01:00,294] [MainThread] DEBUG [memory_util.py:198] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:02:11,814] [MainThread] INFO [memory_util.py:138] [NA] - 创建全局PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 14:02:11,815] [MainThread] INFO [memory_util.py:153] [NA] - 全局PostgreSQL连接池已创建并打开
[2025-06-16 14:02:11,857] [MainThread] DEBUG [memory_util.py:194] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:02:21,898] [MainThread] DEBUG [memory_util.py:198] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:02:21,898] [MainThread] DEBUG [memory_util.py:194] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:02:21,898] [MainThread] DEBUG [memory_util.py:198] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:02:21,905] [MainThread] ERROR [agent_executor.py:71] [NA] - An error occurred while streaming the response: Synchronous calls to AsyncPostgresSaver are only allowed from a different thread. From the main thread, use the async interface. For example, use `await checkpointer.aget_tuple(...)` or `await graph.ainvoke(...)`.
[2025-06-16 14:05:46,128] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:05:46,129] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:05:46,129] [MainThread] INFO [memory_util.py:137] [NA] - 创建全局PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 14:05:46,129] [MainThread] INFO [memory_util.py:152] [NA] - 全局PostgreSQL连接池已创建并打开
[2025-06-16 14:05:46,160] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:05:54,967] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:05:54,967] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:05:54,967] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:05:54,967] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:05:54,968] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:05:54,973] [MainThread] ERROR [agent_executor.py:71] [NA] - An error occurred while streaming the response: Synchronous calls to AsyncPostgresSaver are only allowed from a different thread. From the main thread, use the async interface. For example, use `await checkpointer.aget_tuple(...)` or `await graph.ainvoke(...)`.
[2025-06-16 14:08:52,957] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:08:52,958] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:08:52,958] [MainThread] INFO [memory_util.py:137] [NA] - 创建全局PostgreSQL连接池: postgres://root:123456@127.0.0.1:5432/langgraph_memory?sslmode=disable
[2025-06-16 14:08:52,958] [MainThread] INFO [memory_util.py:152] [NA] - 全局PostgreSQL连接池已创建并打开
[2025-06-16 14:08:52,991] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:09:01,069] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:09:01,070] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:09:01,070] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:09:01,070] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:09:01,070] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:09:01,077] [MainThread] ERROR [agent_executor.py:71] [NA] - An error occurred while streaming the response: Synchronous calls to AsyncPostgresSaver are only allowed from a different thread. From the main thread, use the async interface. For example, use `await checkpointer.aget_tuple(...)` or `await graph.ainvoke(...)`.
[2025-06-16 14:10:33,129] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:10:33,130] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:10:33,130] [MainThread] INFO [memory_util.py:137] [NA] - 创建全局PostgreSQL连接池
[2025-06-16 14:10:33,130] [MainThread] INFO [memory_util.py:152] [NA] - 全局PostgreSQL连接池已创建并打开
[2025-06-16 14:10:33,162] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:10:41,528] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:10:41,528] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:10:41,528] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:10:41,528] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:10:41,528] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:10:41,530] [MainThread] ERROR [agent_executor.py:71] [NA] - An error occurred while streaming the response: 'coroutine' object has no attribute 'values'
[2025-06-16 14:11:19,617] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:11:19,618] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:11:19,618] [MainThread] INFO [memory_util.py:137] [NA] - 创建全局PostgreSQL连接池
[2025-06-16 14:11:19,619] [MainThread] INFO [memory_util.py:152] [NA] - 全局PostgreSQL连接池已创建并打开
[2025-06-16 14:11:19,655] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:11:26,547] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:11:26,547] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:11:26,547] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:11:26,547] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:11:26,552] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:13:34,048] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:13:34,049] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:13:34,049] [MainThread] INFO [memory_util.py:137] [NA] - 创建全局PostgreSQL连接池
[2025-06-16 14:13:34,127] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:13:42,527] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:13:42,527] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:13:42,527] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:13:42,527] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:13:42,531] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:17:33,449] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:17:33,449] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:17:33,449] [MainThread] INFO [memory_util.py:137] [NA] - 创建全局PostgreSQL连接池
[2025-06-16 14:17:33,516] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:17:40,898] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:17:40,898] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:17:40,898] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:17:40,898] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:17:40,904] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:20:16,201] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:20:16,202] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:20:16,202] [MainThread] INFO [memory_util.py:137] [NA] - 创建全局PostgreSQL连接池
[2025-06-16 14:20:16,202] [MainThread] INFO [memory_util.py:152] [NA] - 全局PostgreSQL连接池已创建并打开
[2025-06-16 14:20:16,237] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:20:23,518] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:20:23,518] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:20:23,518] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:20:23,518] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:20:23,522] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:23:49,922] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:23:49,924] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:23:49,924] [MainThread] INFO [memory_util.py:137] [NA] - 创建全局PostgreSQL连接池
[2025-06-16 14:23:49,924] [MainThread] INFO [memory_util.py:152] [NA] - 全局PostgreSQL连接池已创建并打开
[2025-06-16 14:23:49,952] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:23:57,748] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:23:57,748] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:23:57,748] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:23:57,748] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:23:57,754] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:25:31,151] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:25:31,151] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:25:31,151] [MainThread] INFO [memory_util.py:137] [NA] - 创建全局PostgreSQL连接池
[2025-06-16 14:25:31,151] [MainThread] INFO [memory_util.py:152] [NA] - 全局PostgreSQL连接池已创建并打开
[2025-06-16 14:25:31,183] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:25:45,057] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:25:45,057] [MainThread] INFO [memory_util.py:94] [NA] - async memory_type is postgres
[2025-06-16 14:25:45,057] [MainThread] WARNING [memory_util.py:113] [NA] - PostgreSQL需要异步上下文管理器，请使用 async_postgres_memory_saver()，当前返回内存存储作为后备
[2025-06-16 14:25:45,057] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:25:45,062] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:27:39,622] [MainThread] INFO [memory_util.py:137] [NA] - 创建全局PostgreSQL连接池
[2025-06-16 14:27:39,622] [MainThread] INFO [memory_util.py:152] [NA] - 全局PostgreSQL连接池已创建并打开
[2025-06-16 14:27:39,652] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:27:48,018] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
[2025-06-16 14:27:48,018] [MainThread] DEBUG [memory_util.py:193] [NA] - PostgreSQL保存器已创建（使用全局连接池）
[2025-06-16 14:27:48,023] [MainThread] DEBUG [memory_util.py:197] [NA] - PostgreSQL保存器已释放
