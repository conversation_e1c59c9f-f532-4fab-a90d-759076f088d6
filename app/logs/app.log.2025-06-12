[2025-06-12 09:44:56,223] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: 'generator' object is not subscriptable
[2025-06-12 09:48:46,030] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: 35 validation errors for QuestionResult
result.0.content
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.quesType
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.levelType
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.explainText
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.correctOption
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.wrongOption
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.answer
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.content
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.quesType
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.levelType
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.explainText
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.correctOption
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.wrongOption
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.answer
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.content
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.quesType
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.levelType
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.explainText
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.correctOption
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.wrongOption
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.answer
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.content
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.quesType
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.levelType
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.explainText
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.correctOption
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.wrongOption
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.answer
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.content
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.quesType
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.levelType
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.explainText
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.correctOption
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.wrongOption
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.answer
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
[2025-06-12 09:51:38,114] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: 35 validation errors for QuestionResult
result.0.content
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.quesType
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.levelType
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.explainText
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.correctOption
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.wrongOption
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.answer
  Field required [type=missing, input_value={'cotent': '中国的首...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.content
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.quesType
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.levelType
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.explainText
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.correctOption
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.wrongOption
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.answer
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.content
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.quesType
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.levelType
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.explainText
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.correctOption
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.wrongOption
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.answer
  Field required [type=missing, input_value={'cotent': '上海是中...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.content
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.quesType
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.levelType
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.explainText
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.correctOption
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.wrongOption
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.answer
  Field required [type=missing, input_value={'cotent': '北京位于...'错误', 'solution': 0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.content
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.quesType
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.levelType
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.explainText
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.correctOption
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.wrongOption
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.answer
  Field required [type=missing, input_value={'cotent': '北京是中...'错误', 'solution': 1}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
[2025-06-12 09:52:08,440] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: 7 validation errors for QuestionResult
result.0.content
  Field required [type=missing, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.quesType
  Field required [type=missing, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.levelType
  Field required [type=missing, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.explainText
  Field required [type=missing, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.correctOption
  Field required [type=missing, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.wrongOption
  Field required [type=missing, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.answer
  Field required [type=missing, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
[2025-06-12 10:02:04,781] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: 35 validation errors for QuestionResult
result.0.content
  Field required [type=missing, input_value={'cotent': '中国的首...本的地理常识。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.quesType
  Field required [type=missing, input_value={'cotent': '中国的首...本的地理常识。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.levelType
  Field required [type=missing, input_value={'cotent': '中国的首...本的地理常识。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.explainText
  Field required [type=missing, input_value={'cotent': '中国的首...本的地理常识。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.correctOption
  Field required [type=missing, input_value={'cotent': '中国的首...本的地理常识。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.wrongOption
  Field required [type=missing, input_value={'cotent': '中国的首...本的地理常识。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.0.answer
  Field required [type=missing, input_value={'cotent': '中国的首...本的地理常识。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.content
  Field required [type=missing, input_value={'cotent': '北京是中...于省级行政区。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.quesType
  Field required [type=missing, input_value={'cotent': '北京是中...于省级行政区。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.levelType
  Field required [type=missing, input_value={'cotent': '北京是中...于省级行政区。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.explainText
  Field required [type=missing, input_value={'cotent': '北京是中...于省级行政区。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.correctOption
  Field required [type=missing, input_value={'cotent': '北京是中...于省级行政区。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.wrongOption
  Field required [type=missing, input_value={'cotent': '北京是中...于省级行政区。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.1.answer
  Field required [type=missing, input_value={'cotent': '北京是中...于省级行政区。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.content
  Field required [type=missing, input_value={'cotent': '中国的首...都仍然是北京。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.quesType
  Field required [type=missing, input_value={'cotent': '中国的首...都仍然是北京。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.levelType
  Field required [type=missing, input_value={'cotent': '中国的首...都仍然是北京。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.explainText
  Field required [type=missing, input_value={'cotent': '中国的首...都仍然是北京。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.correctOption
  Field required [type=missing, input_value={'cotent': '中国的首...都仍然是北京。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.wrongOption
  Field required [type=missing, input_value={'cotent': '中国的首...都仍然是北京。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.2.answer
  Field required [type=missing, input_value={'cotent': '中国的首...都仍然是北京。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.content
  Field required [type=missing, input_value={'cotent': '北京是中...治和文化中心。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.quesType
  Field required [type=missing, input_value={'cotent': '北京是中...治和文化中心。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.levelType
  Field required [type=missing, input_value={'cotent': '北京是中...治和文化中心。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.explainText
  Field required [type=missing, input_value={'cotent': '北京是中...治和文化中心。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.correctOption
  Field required [type=missing, input_value={'cotent': '北京是中...治和文化中心。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.wrongOption
  Field required [type=missing, input_value={'cotent': '北京是中...治和文化中心。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.3.answer
  Field required [type=missing, input_value={'cotent': '北京是中...治和文化中心。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.content
  Field required [type=missing, input_value={'cotent': '北京位于...，靠近渤海湾。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.quesType
  Field required [type=missing, input_value={'cotent': '北京位于...，靠近渤海湾。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.levelType
  Field required [type=missing, input_value={'cotent': '北京位于...，靠近渤海湾。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.explainText
  Field required [type=missing, input_value={'cotent': '北京位于...，靠近渤海湾。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.correctOption
  Field required [type=missing, input_value={'cotent': '北京位于...，靠近渤海湾。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.wrongOption
  Field required [type=missing, input_value={'cotent': '北京位于...，靠近渤海湾。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
result.4.answer
  Field required [type=missing, input_value={'cotent': '北京位于...，靠近渤海湾。'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
[2025-06-12 10:13:33,508] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: 'AIMessage' object is not subscriptable
[2025-06-12 10:37:15,603] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: TypedDict does not support instance and class checks
[2025-06-12 11:01:35,949] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: 'messages'
[2025-06-12 11:37:24,759] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: object NoneType can't be used in 'await' expression
[2025-06-12 15:24:15,092] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: 'str' object has no attribute 'executescript'
[2025-06-12 15:27:50,970] [MainThread] INFO [memory_util.py:13] [NA] - memory_type is sqlite
[2025-06-12 15:27:50,970] [MainThread] INFO [memory_util.py:13] [NA] - memory_type is sqlite
[2025-06-12 15:27:50,970] [MainThread] INFO [memory_util.py:20] [NA] - sqlite path is data/memory.db
[2025-06-12 15:27:50,970] [MainThread] INFO [memory_util.py:20] [NA] - sqlite path is data/memory.db
[2025-06-12 15:28:06,404] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: 'str' object has no attribute 'executescript'
[2025-06-12 15:28:06,404] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: 'str' object has no attribute 'executescript'
[2025-06-12 15:49:02,691] [MainThread] INFO [memory_util.py:13] [NA] - memory_type is sqlite
[2025-06-12 15:49:02,691] [MainThread] INFO [memory_util.py:13] [NA] - memory_type is sqlite
[2025-06-12 15:49:02,691] [MainThread] INFO [memory_util.py:20] [NA] - sqlite path is data/memory.db
[2025-06-12 15:49:02,691] [MainThread] INFO [memory_util.py:20] [NA] - sqlite path is data/memory.db
[2025-06-12 16:18:35,070] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-12 16:18:35,070] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-12 16:18:35,070] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-12 16:18:35,070] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-12 16:19:39,219] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-12 16:19:39,219] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-12 16:19:39,219] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-12 16:19:39,219] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-12 16:19:40,724] [MainThread] ERROR [agent_executor.py:76] [NA] - An error occurred while streaming the response: 'chunk'
[2025-06-12 16:19:40,724] [MainThread] ERROR [agent_executor.py:76] [NA] - An error occurred while streaming the response: 'chunk'
[2025-06-12 16:22:25,653] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-12 16:22:25,653] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is sqlite
[2025-06-12 16:22:25,653] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-12 16:22:25,653] [MainThread] INFO [memory_util.py:50] [NA] - sqlite path is data/memory.db
[2025-06-12 16:22:27,478] [MainThread] ERROR [agent_executor.py:76] [NA] - An error occurred while streaming the response: The SqliteSaver does not support async methods. Consider using AsyncSqliteSaver instead.
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
Note: AsyncSqliteSaver requires the aiosqlite package to use.
Install with:
`pip install aiosqlite`
See https://langchain-ai.github.io/langgraph/reference/checkpoints/asyncsqlitesaverfor more information.
[2025-06-12 16:22:27,478] [MainThread] ERROR [agent_executor.py:76] [NA] - An error occurred while streaming the response: The SqliteSaver does not support async methods. Consider using AsyncSqliteSaver instead.
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
Note: AsyncSqliteSaver requires the aiosqlite package to use.
Install with:
`pip install aiosqlite`
See https://langchain-ai.github.io/langgraph/reference/checkpoints/asyncsqlitesaverfor more information.
[2025-06-12 16:23:02,572] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:23:02,572] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:23:04,888] [MainThread] ERROR [agent_executor.py:76] [NA] - An error occurred while streaming the response: object NoneType can't be used in 'await' expression
[2025-06-12 16:23:04,888] [MainThread] ERROR [agent_executor.py:76] [NA] - An error occurred while streaming the response: object NoneType can't be used in 'await' expression
[2025-06-12 16:24:23,848] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:24:23,848] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:24:26,391] [MainThread] ERROR [agent_executor.py:76] [NA] - An error occurred while streaming the response: 'AIMessageChunk' object is not subscriptable
[2025-06-12 16:24:26,391] [MainThread] ERROR [agent_executor.py:76] [NA] - An error occurred while streaming the response: 'AIMessageChunk' object is not subscriptable
[2025-06-12 16:30:15,103] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:30:15,103] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:30:17,582] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: 'AIMessageChunk' object is not subscriptable
[2025-06-12 16:30:17,582] [MainThread] ERROR [agent_executor.py:75] [NA] - An error occurred while streaming the response: 'AIMessageChunk' object is not subscriptable
[2025-06-12 16:31:13,872] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:31:13,872] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:33:09,874] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:33:09,874] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:43:29,258] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:43:29,258] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:45:47,814] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:45:47,814] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:46:58,010] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:46:58,010] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:49:03,066] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:49:03,066] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:49:36,535] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 16:49:36,535] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 19:12:39,966] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
[2025-06-12 19:12:39,966] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is memory
