[2025-06-13 13:46:38,862] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 13:50:53,363] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 13:51:57,950] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 13:52:05,334] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 13:56:17,770] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 13:56:23,689] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 13:57:28,441] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 13:59:14,973] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 14:02:28,020] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 14:03:09,776] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 14:03:21,850] [MainThread] INFO [memory_util.py:38] [NA] - memory_type is postgres
[2025-06-13 14:12:42,506] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:13:04,702] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:13:55,697] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:14:34,338] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:14:34,338] [MainThread] INFO [memory_util.py:60] [NA] - postgres conn_string is postgresql://root:123456@localhost:5432/langgraph_memory?sslmode=disable
[2025-06-13 14:15:36,996] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:16:24,364] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:16:33,370] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:16:51,037] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
[2025-06-13 14:17:43,862] [MainThread] INFO [memory_util.py:39] [NA] - memory_type is postgres
