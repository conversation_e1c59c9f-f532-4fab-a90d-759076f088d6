import os
from pathlib import Path

import toml


def _load_config(path: str = "application.toml") -> dict:
    if not os.path.exists(path):
        # 当前文件路径
        current_file = Path(__file__)
        path = str(current_file.resolve().parent.parent.parent / path)  # 可根据子目录层级调整 parent 层数
        if not os.path.exists(path):
            raise FileNotFoundError(f"配置文件不存在: {path}")
    return toml.load(path)


config = _load_config()
