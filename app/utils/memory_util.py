"""
内存保存器工具模块

该模块提供了不同类型的内存保存器实现，用于LangGraph的检查点功能。
支持内存、SQLite和PostgreSQL三种存储方式。
"""

from langgraph.checkpoint.memory import MemorySaver
from psycopg_pool import AsyncConnectionPool

from app.log.log_util import setup_logger
from app.utils.config_util import config
from langgraph.checkpoint.postgres import PostgresSaver
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
# 初始化日志记录器
log = setup_logger()
# 获取配置对象
config = config



def memory_saver():
    """
    根据配置获取相应的内存保存器实例

    该函数根据配置文件中的memory.type设置，返回对应的内存保存器：
    - memory: 使用内存存储（默认）
    - sqlite: 使用SQLite数据库存储
    - postgres: 使用PostgreSQL数据库存储

    Returns:
        MemorySaver | SqliteSaver | PostgresSaver: 对应类型的内存保存器实例

    Raises:
        ValueError: 当配置的memory类型不受支持时抛出异常
    """
    # 从配置中获取内存相关配置，如果没有则使用空字典
    memory_config = config.get("memory", {})
    # 获取内存类型，默认为"memory"
    memory_type = memory_config.get("type", "memory")
    log.info(f"memory_type is {memory_type}")

    # 根据配置的内存类型返回相应的保存器实例
    if memory_type == "memory":
        # 使用内存存储，数据仅在程序运行期间保存
        return MemorySaver()

    elif memory_type == "sqlite":
        # 使用SQLite数据库存储
        sqlite_conf = memory_config.get("sqlite", {})
        # 获取数据库文件路径，默认为"memory.db"
        db_path = sqlite_conf.get("path", "memory.db")
        log.info(f"sqlite path is {db_path}")
        from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
        return AsyncSqliteSaver(db_path)

    elif memory_type == "postgres":
        # 使用PostgreSQL数据库存储
        pg_conf = memory_config.get("postgres", {})
        conn_string = f"postgres://{pg_conf.get('user')}:{pg_conf.get('password')}@{pg_conf.get('host')}:{pg_conf.get('port')}/{pg_conf.get('database')}?sslmode=disable"
        # 构建PostgreSQL连接URL并创建保存器实例
        return PostgresSaver.from_conn_string(
            conn_string=conn_string
        )

    else:
        # 抛出异常，表示不支持的内存类型
        raise ValueError(f"不支持的 memory 类型: {memory_type}")

def amemory_saver():
    """
    根据配置获取相应的内存保存器实例

    该函数根据配置文件中的memory.type设置，返回对应的内存保存器：
    - memory: 使用内存存储（默认）
    - sqlite: 使用SQLite数据库存储
    - postgres: 使用PostgreSQL数据库存储

    Returns:
        MemorySaver | SqliteSaver | PostgresSaver: 对应类型的内存保存器实例

    Raises:
        ValueError: 当配置的memory类型不受支持时抛出异常
    """
    # 从配置中获取内存相关配置，如果没有则使用空字典
    memory_config = config.get("memory", {})
    # 获取内存类型，默认为"memory"
    memory_type = memory_config.get("type", "memory")
    log.info(f"memory_type is {memory_type}")

    # 根据配置的内存类型返回相应的保存器实例
    if memory_type == "memory":
        # 使用内存存储，数据仅在程序运行期间保存
        return MemorySaver()

    elif memory_type == "sqlite":
        # 使用SQLite数据库存储
        sqlite_conf = memory_config.get("sqlite", {})
        # 获取数据库文件路径，默认为"memory.db"
        db_path = sqlite_conf.get("path", "memory.db")
        log.info(f"sqlite path is {db_path}")
        from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
        return AsyncSqliteSaver(db_path)

    elif memory_type == "postgres":
        # 使用PostgreSQL数据库存储
        pg_conf = memory_config.get("postgres", {})
        conn_string = f"postgres://{pg_conf.get('user')}:{pg_conf.get('password')}@{pg_conf.get('host')}:{pg_conf.get('port')}/{pg_conf.get('database')}?sslmode=disable"
        log.info(f"postgres conn_string is {conn_string}")
        # 使用异步PostgreSQL保存器
        from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
        return AsyncPostgresSaver.from_conn_string(conn_string)

    else:
        # 抛出异常，表示不支持的内存类型
        raise ValueError(f"不支持的 memory 类型: {memory_type}")


# amemory_saver()