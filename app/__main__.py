import asyncio
import sys

import click
import httpx
import uvicorn
from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers import DefaultRequestHandler
from a2a.server.tasks import InMemoryPushNotifier, InMemoryTaskStore
from a2a.types import (
    AgentCapabilities,
    AgentCard,
    AgentSkill,
)
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.agent_executor import GenQuestionExecutor

apiApp = FastAPI()
# 配置CORS
apiApp.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应该限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@click.command()
@click.option('--host', 'host', default='localhost')
@click.option('--port', 'port', default=10000)
def main(host, port):
  skill = AgentSkill(
      id='gen_questions',
      name='AI出题工具',
      description='AI出题工具',
      tags=['gen questions', 'generated questions'],
  )
  capabilities = AgentCapabilities(streaming=True, pushNotifications=True)
  httpx_client = httpx.AsyncClient()
  request_handler = DefaultRequestHandler(
      agent_executor=GenQuestionExecutor(),
      task_store=InMemoryTaskStore(),
      push_notifier=InMemoryPushNotifier(httpx_client),
  )
  agent_card = AgentCard(
      name='AI出题工具',
      description='AI出题工具',
      url=f'http://{host}:{port}/card',
      version='1.0.0',
      defaultInputModes=['text', 'text/plain'],
      defaultOutputModes=['text', 'text/plain'],
      capabilities=capabilities,
      skills=[skill],
  )
  app = A2AStarletteApplication(
      agent_card=agent_card, http_handler=request_handler
  )
  app_server = app.build()
  app_server.mount("/", apiApp)

  @apiApp.get("/server_status")
  async def root():
    """服务健康检查接口"""
    return {"status": "running"}

  uvicorn.run(app_server, host=host, port=port)



if __name__ == '__main__':
    if sys.platform.startswith('win'):
        from asyncio import WindowsSelectorEventLoopPolicy
        asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())
    main()