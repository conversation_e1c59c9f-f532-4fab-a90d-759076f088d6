import asyncio
import json
import os
import sys
from typing import Literal, AsyncIterable, Any, Optional, TypedDict, Annotated, \
    AsyncGenerator

from langchain_core.messages import AIMessage, ToolMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.constants import START, END
from langgraph.graph import StateGraph, add_messages, MessagesState
from langgraph.prebuilt import create_react_agent
from openai.types.responses import ParsedContent
from pydantic import BaseModel

from app.utils.memory_util import amemory_saver, memory_saver

# 设置大模型代理
os.environ["OPENAI_API_BASE"] = "https://ymcas-llm.yxt.com/ymcas-ai/multi-model/v1"
os.environ["OPENAI_API_KEY"] = "RD-consultant-ac507e41-1230-426f-b3a6-c78c7ee4e6e0"


class ResponseFormat(BaseModel):
    """Respond to the user in this format."""

    status: Literal['input_required', 'completed', 'error'] = 'input_required'
    message: str


class GenQuestionAgent:
    SYSTEM_INSTRUCTION = (
        '''## 背景（Background）
    为了高效且精准地评估学员的学习成效，需要快速设计并生成一批高质量的判断题。这些试题将严格基于给定的出题内容，并可选地针对特定知识点进行定制。同时，确保试题能够按照指定的语言版本输出，且仅生成判断题类型,不要反问我，直接输出问题就行。
    
    ## 角色（Character）
    你是一位资深的出卷组专家，精通多语种试题设计与生成，尤其擅长快速生成判断题。你需要根据输入的素材和要求，设计符合标准且无冗余的试题，并翻译成指定的语言版本。
    
    ## 输入参数
    - `[[content]]`：'<<<全局变量_出题内容=constants.content>>>'   #包含潜在题目素材的文档，其内容语言为中文或英文。
    - `[[kngPoint]]`（可选）：'<<<全局变量_知识点=constants.kngPoint>>>'  #一个或多个具体的知识点标签，用于指导试题的生成。未提供时，试题将基于整体出题内容生成。
    - `[[difficulty_levels]]`：'<<<全局变量_难度级别=constants.difficulty_levels>>>'  #整数，指示难度级别的数量。决定难度标识的种类，如3级对应[难]、[中]、[易]，5级对应[困难]、[较难]、[中]、[较易]、[易]。
    - `[[count]]`：'<<<全局变量_判断题数量=constants.true_number>>>'  #整数，明确指定需要生成的试题数量。
    - `[[lang]]`：'<<<全局变量_试题语言版本=constants.lang>>>'  #目标语言版本，明确使用该语言输出对应的内容。
    
    ## 输出的JSON结构定义
    输出的JSON对象将包含一个名为result的键，其值是一个数组，数组中的每个元素都是一个试题对象，试题对象包含以下字段（按此顺序）：
    - `content`：字符串，试题的题干部分。
    - `quesType`：整数，固定为2，表示判断题类型。
    - `levelType`：整数，试题的难度级别，根据`[[difficulty_levels]]`确定（0：易；1：中；2：困难；3：较易；4：较难；）。
    - `explainText`：字符串，对试题的解析或答案说明。
    - `correctOption`：固定字符串，中文“正确”的字符。
    - `wrongOption`：固定字符串，中文“错误”的字符。
    - `answer`：整数，判断题结果。0：错误；1：正确。
    
    ## 行动（Action）
    
    ### 1. 预处理
    - **内容读取**：读取`[[content]]`，验证其内容的完整性和可读性。
    - **语言验证**：验证`[[lang]]`的有效性，确保支持该语言版本。
    
    ### 2. 内容解析与知识点筛选
    - **解析内容**：从`[[content]]`中提取与考试相关的素材，并转换为判断题形式。
    - **知识点匹配**：如果提供了`[[kngPoint]]`，则筛选与这些知识点直接相关的素材作为试题基础。
    
    ### 3. 难度分配
    - **确定难度标识**：根据`[[difficulty_levels]]`的值，确定难度标识的种类。
    - **分配难度**：基于试题的复杂性和知识深度，为每道试题分配适当的难度标识。
    
    ### 4. 试题生成
    - **构建试题结构**：按照上述定义的JSON结构生成判断题，确保每个字段的准确性和必要性。
    - **生成题干**：基于筛选后的素材，编写判断题的题干部分。
    - **设置答案与解析**：为每道试题设置正确答案和解析，确保解析的准确性和启发性。在answer中设置正确答案。答案和解析要保持一致性。特别注意，判断题的正确答案可能是“错误”。
    
    ### 5. 格式校验与输出
    - **数量验证**：确保生成的试题数量与`[[count]]`一致。
    - **格式检查**：验证试题的格式是否符合预定义的json结构，包括题型、难易度、答案和解析的排列顺序。
    - **语言检查**：确保所有输出内容均为`[[lang]]`语言版本，并正确显示`correctOption`和`wrongOption`。
    - **异常处理**：对于任何格式错误或内容不完整的情况，进行错误记录和异常处理。
    - **输出试题**：以标准化的json格式输出试题列表，确保每道试题都符合评估要求且易于理解。
    
    ## 输出的格式要求
    - 输出的试题应简洁明了，直接关联到试题的关键知识点。
    - 所有试题均来源于`[[content]]`，并符合`[[kngPoint]]`（如果提供）的要求。
    - 试题数量严格等于`[[count]]`。
    - 试题中不得包含任何与评估无关的信息或分析过程。
    - 严格以json格式返回，key为result。
    - 在生成JSON时，确保按照content, quesType, levelType, explainText, correctOption, wrongOption, answer等的顺序添加属性到每个试题对象中。
    
    ## 示例输出
    {
        "result": [{
            "content": "题目内容",
            "quesType": 2,
            "levelType": 1,
            "explainText": "答案解析.",
            "correctOption": "正确",
            "wrongOption": "错误",
            "answer": 0
        }]
    }
    
    # 输出前校验
    - 如果答案解析认为题干表述是正确的，那么answer应该返回1。
    - 如果答案解析认为题干表述是错误的，那么answer应该返回0。'''
    )

    def __init__(self):
        self.model = ChatOpenAI(model="doubao-pro-32k-241215", temperature=0, streaming=True)
        self.tools = []

        def create_prompt(user_input: Any) -> list:
            return [
                {"role": "system", "content": self.SYSTEM_INSTRUCTION},
                {"role": "user", "content": user_input},
            ]

        # 定义调用模型的函数
        async def call_model(state: MessagesState) -> AsyncGenerator[dict, None]:
            messages = create_prompt(state['messages'][-1].content)
            async for chunk in self.model.astream(messages):
                yield {"messages": [{"role": "assistant", "content": chunk.content}]}

        graph = StateGraph(MessagesState)
        graph.add_node("call_model", call_model)
        graph.add_edge(START, "call_model")
        graph.add_edge("call_model", END)

        with amemory_saver() as memory:
            self.graph = graph.compile(checkpointer=memory)



    def invoke(self, query, context_id) -> dict:
        config: RunnableConfig = {'configurable': {'thread_id': context_id}}
        self.graph.invoke({'messages': [('user', query)]}, config)
        return self.get_agent_response(config)

    async def stream(self, query, context_id) -> AsyncIterable[dict[str, Any]]:
        inputs = {'messages': [('user', query)]}
        config: RunnableConfig = {'configurable': {'thread_id': context_id}}
        buffer = ""
        async for event in self.graph.astream_events(inputs, config):
            if event['event'] == "on_chat_model_stream":
                delta = event['data']["chunk"].content
                if delta:
                    buffer += delta
                    # 条件1：到20个字符，或 条件2：句末（句号、问号、感叹号等）
                    if len(buffer) >= 20 or buffer[-1] in "。！？!.?":
                        yield {
                            "is_task_complete": False,
                            "require_user_input": False,
                            "content": buffer
                        }
                        buffer = ""
            elif event['event'] == "on_llm_end":
                if buffer:
                    yield {
                        "is_task_complete": False,
                        "require_user_input": False,
                        "content": buffer
                    }
                break

        yield self.get_agent_response(config)

    def get_agent_response(self, config):
        current_state = self.graph.get_state(config)
        return {
            'is_task_complete': True,
            'require_user_input': False,
            'content': (
                current_state.values['messages'][-1].content
            ),
        }

    SUPPORTED_CONTENT_TYPES = ['text', 'text/plain']
